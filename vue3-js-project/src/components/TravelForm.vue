<template>
  <a-card title="旅游信息设置" class="travel-form-card">
    <a-form layout="vertical">
      <!-- 目的地 -->
      <a-form-item label="目的地" required>
        <a-select
          v-model:value="destination"
          placeholder="请选择或输入目的地"
          show-search
          :options="destinationOptions"
          @search="handleDestinationSearch"
        />
      </a-form-item>

      <!-- 日期范围 -->
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="开始日期" required>
            <a-date-picker
              v-model:value="startDate"
              placeholder="选择开始日期"
              :disabled-date="disabledStartDate"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="结束日期" required>
            <a-date-picker
              v-model:value="endDate"
              placeholder="选择结束日期"
              :disabled-date="disabledEndDate"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 交通方式 -->
      <a-form-item label="交通方式">
        <a-radio-group v-model:value="transportation">
          <a-radio value="public">公共交通</a-radio>
          <a-radio value="self-driving">自驾</a-radio>
        </a-radio-group>
      </a-form-item>

      <!-- 预算 -->
      <a-form-item label="预算范围">
        <a-slider
          v-model:value="budget"
          :min="1000"
          :max="10000"
          :step="500"
          :marks="budgetMarks"
        />
        <div style="text-align: center; margin-top: 8px">
          <a-tag color="blue">¥{{ budget }}</a-tag>
        </div>
      </a-form-item>

      <!-- 旅行风格 -->
      <a-form-item label="旅行风格">
        <a-select v-model:value="travelStyle" style="width: 100%">
          <a-select-option value="relaxed">休闲放松</a-select-option>
          <a-select-option value="adventurous">探险刺激</a-select-option>
          <a-select-option value="cultural">文化体验</a-select-option>
        </a-select>
      </a-form-item>

      <!-- 操作按钮 -->
      <a-form-item>
        <a-space>
          <a-button
            type="primary"
            :loading="loading"
            @click="handleGenerate"
            :disabled="!canGenerate"
          >
            生成攻略
          </a-button>
          <a-button @click="handleReset">重置</a-button>
        </a-space>
      </a-form-item>

      <!-- 提示信息 -->
      <a-alert
        v-if="totalDays > 0"
        type="info"
        :message="`共 ${totalDays} 天行程`"
        show-icon
        style="margin-bottom: 16px"
      />
      
      <a-alert
        v-if="error"
        type="error"
        :message="error"
        show-icon
        style="margin-bottom: 16px"
      />
    </a-form>
  </a-card>
</template>

<script setup>
import { computed, watch } from 'vue'
import dayjs from 'dayjs'
import { useItineraryStore } from '@/stores/itinerary'
import { storeToRefs } from 'pinia'

// 使用 Pinia 状态管理
const itineraryStore = useItineraryStore()

// 使用 storeToRefs 保持响应性
const {
  destination,
  startDate,
  endDate,
  transportation,
  budget,
  travelStyle,
  loading,
  error,
  totalDays,
  itinerary
} = storeToRefs(itineraryStore)

// 获取方法（方法不需要 storeToRefs）
const { generateItinerary, resetItinerary } = itineraryStore

// 监听表单数据变化
watch(destination, (newValue) => {
  console.log('目的地变化:', newValue)
})

watch(travelStyle, (newValue) => {
  console.log('旅行风格变化:', newValue)
})

// 目的地选项
const destinationOptions = [
  { value: '北京', label: '北京' },
  { value: '上海', label: '上海' },
  { value: '杭州', label: '杭州' },
  { value: '西安', label: '西安' },
  { value: '成都', label: '成都' },
  { value: '广州', label: '广州' },
  { value: '深圳', label: '深圳' },
  { value: '厦门', label: '厦门' },
  { value: '三亚', label: '三亚' },
  { value: '丽江', label: '丽江' }
]

// 预算标记
const budgetMarks = {
  1000: '1k',
  3000: '3k',
  5000: '5k',
  7000: '7k',
  10000: '10k'
}

// 是否可以生成攻略
const canGenerate = computed(() => {
  return destination.value && startDate.value && endDate.value
})

// 处理目的地搜索
const handleDestinationSearch = (value) => {
  console.log('搜索目的地:', value)
  // 可以在这里添加搜索建议逻辑
}

// 禁用开始日期
const disabledStartDate = (current) => {
  return current && current < dayjs().startOf('day')
}

// 禁用结束日期
const disabledEndDate = (current) => {
  if (!startDate.value) return current && current < dayjs().startOf('day')
  return current && current < dayjs(startDate.value).startOf('day')
}

// 处理生成攻略
const handleGenerate = () => {
  console.log('点击生成攻略按钮')
  console.log('当前表单数据:', {
    destination: destination.value,
    startDate: startDate.value,
    endDate: endDate.value,
    transportation: transportation.value,
    budget: budget.value,
    travelStyle: travelStyle.value
  })
  generateItinerary()
}

// 处理重置
const handleReset = () => {
  destination.value = ''
  startDate.value = null
  endDate.value = null
  transportation.value = 'public'
  budget.value = 3000
  travelStyle.value = 'relaxed'
  resetItinerary()
}
</script>

<style scoped>
.travel-form-card {
  margin-bottom: 24px;
  position: sticky;
  top: 24px;
}

/* 宽屏优化 */
@media (min-width: 1200px) {
  .travel-form-card {
    margin-bottom: 32px;
  }
}

@media (min-width: 1600px) {
  .travel-form-card {
    margin-bottom: 40px;
    top: 32px;
  }
}

/* 确保表单在宽屏上不会过宽 */
@media (min-width: 1920px) {
  .travel-form-card {
    max-width: 400px;
  }
}
</style>

