<template>
  <div v-if="itinerary">
    <!-- 攻略概览 -->
    <a-card title="攻略概览" class="mb-4">
      <a-descriptions :column="{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3, xxl: 3 }" bordered>
        <a-descriptions-item label="目的地">{{ itinerary.destination }}</a-descriptions-item>
        <a-descriptions-item label="总天数">{{ itinerary.totalDays }}天</a-descriptions-item>
        <a-descriptions-item label="开始日期">{{ itinerary.startDate }}</a-descriptions-item>
        <a-descriptions-item label="结束日期">{{ itinerary.endDate }}</a-descriptions-item>
        <a-descriptions-item label="交通方式">{{ itinerary.transportation }}</a-descriptions-item>
        <a-descriptions-item label="总预算">
          <a-tag color="green">¥{{ itinerary.budget.total }}</a-tag>
        </a-descriptions-item>
      </a-descriptions>
    </a-card>

    <!-- 费用统计 -->
    <a-card title="费用预算" class="mb-4">
      <a-row :gutter="16">
        <a-col :xs="12" :sm="12" :md="6" :lg="6" :xl="6" :xxl="6" v-for="item in budgetItems" :key="item.key">
          <a-statistic
            :title="item.title"
            :value="item.value"
            :prefix="item.prefix"
            :value-style="{ color: item.color }"
          />
        </a-col>
      </a-row>
    </a-card>

    <!-- 每日行程 -->
    <a-card title="每日详细行程">
      <a-timeline>
        <a-timeline-item
          v-for="day in itinerary.schedule"
          :key="day.day"
          :color="getDayColor(day.day)"
        >
          <template #dot>
            <div class="day-dot">{{ day.day }}</div>
          </template>
          
          <a-card size="small" :title="`第${day.day}天 - ${day.date} (${day.weekday})`">
            <template #extra>
              <a-tag color="blue">预算: ¥{{ day.budget }}</a-tag>
            </template>
            
            <a-timeline>
              <a-timeline-item
                v-for="activity in day.activities"
                :key="activity.name + activity.time"
                :color="getActivityColor(activity.type)"
              >
                <div class="activity-item">
                  <div class="activity-time">{{ activity.time }}</div>
                  <div class="activity-name">{{ activity.name }}</div>
                  <div class="activity-location">
                    <a-tag size="small">{{ activity.location }}</a-tag>
                  </div>
                  <div class="activity-cost">
                    <a-tag size="small" color="green">¥{{ activity.cost }}</a-tag>
                  </div>
                  <div class="activity-notes" v-if="activity.notes">
                    <a-text type="secondary">{{ activity.notes }}</a-text>
                  </div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </a-card>
        </a-timeline-item>
      </a-timeline>
    </a-card>

    <!-- 导出按钮 -->
    <a-card class="mt-4">
      <a-space>
        <a-button type="primary" @click="exportToExcel">
          <template #icon>
            <DownloadOutlined />
          </template>
          导出Excel
        </a-button>
        <a-button @click="exportToExcel('详细版')">
          <template #icon>
            <FileExcelOutlined />
          </template>
          导出详细版
        </a-button>
      </a-space>
    </a-card>
  </div>

  <!-- 空状态 -->
  <a-empty v-else description="请先生成旅游攻略">
    <template #image>
      <div style="font-size: 48px">🗺️</div>
    </template>
  </a-empty>
</template>

<script setup>
import { computed } from 'vue'
import { DownloadOutlined, FileExcelOutlined } from '@ant-design/icons-vue'
import { useExcelExport } from '@/composables/useExcelExport'

const props = defineProps({
  itinerary: {
    type: Object,
    default: null
  }
})

const { exportToExcel: exportExcel } = useExcelExport()

const budgetItems = computed(() => {
  if (!props.itinerary?.budget) return []
  
  const budget = props.itinerary.budget
  return [
    { key: 'transportation', title: '交通费用', value: budget.transportation, prefix: '¥', color: '#3f8600' },
    { key: 'accommodation', title: '住宿费用', value: budget.accommodation, prefix: '¥', color: '#722ed1' },
    { key: 'food', title: '餐饮费用', value: budget.food, prefix: '¥', color: '#fa8c16' },
    { key: 'tickets', title: '门票费用', value: budget.tickets, prefix: '¥', color: '#1890ff' }
  ]
})

const getDayColor = (day) => {
  const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1']
  return colors[(day - 1) % colors.length]
}

const getActivityColor = (type) => {
  const colorMap = {
    attraction: '#1890ff',
    meal: '#52c41a',
    transportation: '#faad14',
    accommodation: '#722ed1'
  }
  return colorMap[type] || '#d9d9d9'
}

const exportToExcel = (type = '标准版') => {
  if (!props.itinerary) return
  
  const fileName = type === '详细版' 
    ? `${props.itinerary.destination}详细旅游攻略.xlsx`
    : `${props.itinerary.destination}旅游攻略.xlsx`
  
  exportExcel(props.itinerary, fileName)
}
</script>

<style scoped>
.mb-4 {
  margin-bottom: 16px;
}
.mt-4 {
  margin-top: 16px;
}
.day-dot {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #1890ff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}
.activity-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.activity-time {
  font-weight: bold;
  color: #1890ff;
}
.activity-name {
  font-size: 16px;
  font-weight: 500;
}
.activity-location {
  margin-top: 4px;
}
.activity-cost {
  margin-top: 4px;
}
.activity-notes {
  margin-top: 4px;
  font-size: 14px;
}

/* 宽屏优化 */
@media (min-width: 1200px) {
  .mb-4 {
    margin-bottom: 24px;
  }

  .activity-item {
    padding: 8px 0;
  }
}

@media (min-width: 1600px) {
  .mb-4 {
    margin-bottom: 32px;
  }

  .activity-item {
    padding: 12px 0;
  }

  .activity-name {
    font-size: 18px;
  }

  .activity-time {
    font-size: 16px;
  }
}
</style>

