<template>
  <div class="travel-planner">
    <a-page-header
      title="智能旅游攻略生成器"
      sub-title="选择目的地、时间和偏好，一键生成完美行程"
      class="page-header"
    />
    
    <div class="content-wrapper">
      <a-row :gutter="24">
        <!-- 左侧表单 -->
        <a-col :xs="24" :md="24" :lg="10" :xl="8" :xxl="6">
          <TravelForm />
        </a-col>

        <!-- 右侧攻略展示 -->
        <a-col :xs="24" :md="24" :lg="14" :xl="16" :xxl="18">
          <ItineraryDisplay :itinerary="itinerary" />
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup>
import { computed, watch } from 'vue'
import TravelForm from '@/components/TravelForm.vue'
import ItineraryDisplay from '@/components/ItineraryDisplay.vue'
import { useItineraryStore } from '@/stores/itinerary'
import { storeToRefs } from 'pinia'

// 使用 Pinia 状态管理
const itineraryStore = useItineraryStore()
const { itinerary } = storeToRefs(itineraryStore)

// 监听攻略数据变化
watch(itinerary, (newItinerary) => {
  console.log('攻略数据更新:', newItinerary)
}, { deep: true })
</script>

<style scoped>
.travel-planner {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px;
}

.page-header {
  background: white;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.content-wrapper {
  max-width: 1600px;
  margin: 0 auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .travel-planner {
    padding: 12px;
  }
}

@media (min-width: 1200px) {
  .content-wrapper {
    max-width: 1400px;
  }
}

@media (min-width: 1600px) {
  .content-wrapper {
    max-width: 1600px;
  }

  .travel-planner {
    padding: 32px;
  }
}

@media (min-width: 1920px) {
  .content-wrapper {
    max-width: 1800px;
  }

  .travel-planner {
    padding: 40px;
  }
}
</style>
