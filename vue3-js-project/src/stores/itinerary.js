import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { calculateDaysBetween, generateDateRange, getDateRangeWithWeekdays } from '@/utils/dateUtils'

/**
 * 旅游攻略状态管理
 */
export const useItineraryStore = defineStore('itinerary', () => {
  const destination = ref('')
  const startDate = ref('')
  const endDate = ref('')
  const transportation = ref('public') // public 或 self-driving
  const budget = ref(3000)
  const travelStyle = ref('relaxed') // relaxed, adventurous, cultural
  
  const itinerary = ref(null)
  const loading = ref(false)
  const error = ref(null)

  // 计算总天数
  const totalDays = computed(() => {
    if (!startDate.value || !endDate.value) return 0
    return calculateDaysBetween(startDate.value, endDate.value)
  })

  // 计算日期范围
  const dateRange = computed(() => {
    if (!startDate.value || !endDate.value) return []
    return getDateRangeWithWeekdays(startDate.value, endDate.value)
  })

  // 生成攻略
  const generateItinerary = async () => {
    console.log("生成攻略");
    if (!destination.value || !startDate.value || !endDate.value) {
      error.value = '请填写完整的目的地和日期信息'      
      return
    }

    loading.value = true
    error.value = null
    
    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const schedule = generateSchedule()
      console.log("生成的行程:", schedule);
      itinerary.value = {
        destination: destination.value,
        startDate: startDate.value,
        endDate: endDate.value,
        totalDays: totalDays.value,
        transportation: transportation.value === 'public' ? '公共交通' : '自驾',
        budget: generateBudget(),
        schedule
      }
      
    } catch (err) {
      error.value = '生成攻略失败，请稍后重试'
      console.error(err)
    } finally {
      loading.value = false
    }
  }

  // 生成行程安排
  const generateSchedule = () => {
    console.log("generateSchedule");
    const dates = dateRange.value
    const schedule = []
    
    // 根据目的地和旅行风格生成不同的行程
    const attractions = getAttractionsByDestination(destination.value)
    const activities = getActivitiesByStyle(travelStyle.value)
    
    dates.forEach((dateInfo, index) => {
      const day = index + 1
      const dayAttractions = selectAttractionsForDay(attractions, day, totalDays.value)
      const dayActivities = generateDayActivities(dayAttractions)
      
      schedule.push({
        day,
        date: dateInfo.date,
        weekday: dateInfo.weekday,
        transportation: transportation.value === 'public' ? '地铁/公交' : '自驾',
        budget: Math.round(budget.value / totalDays.value),
        locations: dayAttractions.map(a => a.location),
        activities: dayActivities
      })
    })
    
    return schedule
  }

  // 根据目的地获取景点
  const getAttractionsByDestination = (dest) => {
    console.log('获取景点数据，目的地:', dest)
    const attractionsMap = {
      '北京': [
        { name: '天安门广场', location: '天安门', cost: 0, time: '1小时', type: 'landmark' },
        { name: '故宫博物院', location: '故宫', cost: 60, time: '4小时', type: 'cultural' },
        { name: '景山公园', location: '景山公园', cost: 2, time: '1小时', type: 'scenic' },
        { name: '八达岭长城', location: '长城', cost: 45, time: '3小时', type: 'historical' },
        { name: '颐和园', location: '颐和园', cost: 30, time: '3小时', type: 'scenic' },
        { name: '圆明园', location: '圆明园', cost: 10, time: '2小时', type: 'historical' },
        { name: '天坛公园', location: '天坛', cost: 15, time: '2小时', type: 'cultural' },
        { name: '北海公园', location: '北海公园', cost: 10, time: '2小时', type: 'scenic' }
      ],
      '上海': [
        { name: '外滩', location: '外滩', cost: 0, time: '2小时', type: 'landmark' },
        { name: '东方明珠', location: '陆家嘴', cost: 180, time: '2小时', type: 'landmark' },
        { name: '豫园', location: '豫园', cost: 40, time: '2小时', type: 'cultural' },
        { name: '南京路步行街', location: '南京路', cost: 0, time: '3小时', type: 'shopping' },
        { name: '上海迪士尼乐园', location: '迪士尼', cost: 399, time: '1天', type: 'entertainment' },
        { name: '田子坊', location: '田子坊', cost: 0, time: '2小时', type: 'cultural' }
      ],
      '杭州': [
        { name: '西湖', location: '西湖', cost: 0, time: '3小时', type: 'scenic' },
        { name: '灵隐寺', location: '灵隐寺', cost: 45, time: '2小时', type: 'cultural' },
        { name: '雷峰塔', location: '雷峰塔', cost: 40, time: '1小时', type: 'historical' },
        { name: '西溪湿地', location: '西溪湿地', cost: 80, time: '3小时', type: 'scenic' },
        { name: '宋城', location: '宋城', cost: 290, time: '4小时', type: 'entertainment' }
      ],
      '成都': [
        { name: '宽窄巷子', location: '宽窄巷子', cost: 0, time: '2小时', type: 'cultural' },
        { name: '锦里古街', location: '锦里', cost: 0, time: '2小时', type: 'cultural' },
        { name: '武侯祠', location: '武侯祠', cost: 50, time: '2小时', type: 'historical' },
        { name: '杜甫草堂', location: '杜甫草堂', cost: 50, time: '2小时', type: 'cultural' },
        { name: '大熊猫繁育研究基地', location: '熊猫基地', cost: 55, time: '3小时', type: 'scenic' },
        { name: '青城山', location: '青城山', cost: 90, time: '4小时', type: 'scenic' },
        { name: '都江堰', location: '都江堰', cost: 80, time: '3小时', type: 'historical' },
        { name: '春熙路', location: '春熙路', cost: 0, time: '2小时', type: 'shopping' }
      ],
      '西安': [
        { name: '兵马俑', location: '兵马俑', cost: 120, time: '4小时', type: 'historical' },
        { name: '华清宫', location: '华清宫', cost: 110, time: '3小时', type: 'historical' },
        { name: '大雁塔', location: '大雁塔', cost: 40, time: '2小时', type: 'cultural' },
        { name: '西安城墙', location: '城墙', cost: 54, time: '2小时', type: 'historical' },
        { name: '回民街', location: '回民街', cost: 0, time: '2小时', type: 'cultural' },
        { name: '陕西历史博物馆', location: '历史博物馆', cost: 0, time: '3小时', type: 'cultural' },
        { name: '大唐芙蓉园', location: '芙蓉园', cost: 120, time: '3小时', type: 'entertainment' },
        { name: '华山', location: '华山', cost: 180, time: '1天', type: 'scenic' }
      ],
      '广州': [
        { name: '广州塔', location: '广州塔', cost: 150, time: '2小时', type: 'landmark' },
        { name: '陈家祠', location: '陈家祠', cost: 10, time: '2小时', type: 'cultural' },
        { name: '沙面岛', location: '沙面岛', cost: 0, time: '2小时', type: 'cultural' },
        { name: '白云山', location: '白云山', cost: 5, time: '3小时', type: 'scenic' },
        { name: '长隆野生动物世界', location: '长隆', cost: 300, time: '1天', type: 'entertainment' },
        { name: '上下九步行街', location: '上下九', cost: 0, time: '2小时', type: 'shopping' },
        { name: '越秀公园', location: '越秀公园', cost: 0, time: '2小时', type: 'scenic' }
      ],
      '深圳': [
        { name: '世界之窗', location: '世界之窗', cost: 200, time: '4小时', type: 'entertainment' },
        { name: '欢乐谷', location: '欢乐谷', cost: 230, time: '1天', type: 'entertainment' },
        { name: '大梅沙海滨公园', location: '大梅沙', cost: 0, time: '3小时', type: 'scenic' },
        { name: '东门老街', location: '东门', cost: 0, time: '2小时', type: 'shopping' },
        { name: '莲花山公园', location: '莲花山', cost: 0, time: '2小时', type: 'scenic' },
        { name: '深圳湾公园', location: '深圳湾', cost: 0, time: '2小时', type: 'scenic' }
      ],
      '厦门': [
        { name: '鼓浪屿', location: '鼓浪屿', cost: 100, time: '4小时', type: 'scenic' },
        { name: '南普陀寺', location: '南普陀寺', cost: 0, time: '2小时', type: 'cultural' },
        { name: '厦门大学', location: '厦大', cost: 0, time: '2小时', type: 'cultural' },
        { name: '环岛路', location: '环岛路', cost: 0, time: '3小时', type: 'scenic' },
        { name: '曾厝垵', location: '曾厝垵', cost: 0, time: '2小时', type: 'cultural' },
        { name: '胡里山炮台', location: '胡里山', cost: 25, time: '1小时', type: 'historical' }
      ],
      '三亚': [
        { name: '亚龙湾', location: '亚龙湾', cost: 0, time: '4小时', type: 'scenic' },
        { name: '天涯海角', location: '天涯海角', cost: 101, time: '3小时', type: 'scenic' },
        { name: '南山文化旅游区', location: '南山', cost: 145, time: '4小时', type: 'cultural' },
        { name: '蜈支洲岛', location: '蜈支洲岛', cost: 168, time: '1天', type: 'scenic' },
        { name: '大东海', location: '大东海', cost: 0, time: '3小时', type: 'scenic' },
        { name: '椰梦长廊', location: '椰梦长廊', cost: 0, time: '2小时', type: 'scenic' }
      ],
      '丽江': [
        { name: '丽江古城', location: '古城', cost: 50, time: '3小时', type: 'cultural' },
        { name: '玉龙雪山', location: '玉龙雪山', cost: 130, time: '1天', type: 'scenic' },
        { name: '束河古镇', location: '束河', cost: 40, time: '3小时', type: 'cultural' },
        { name: '泸沽湖', location: '泸沽湖', cost: 100, time: '1天', type: 'scenic' },
        { name: '黑龙潭公园', location: '黑龙潭', cost: 0, time: '2小时', type: 'scenic' },
        { name: '木府', location: '木府', cost: 60, time: '2小时', type: 'historical' }
      ]
    }

    const result = attractionsMap[dest] || attractionsMap['北京']
    console.log(`目的地 "${dest}" 的景点数据:`, result.slice(0, 3).map(a => a.name))
    return result
  }

  // 根据旅行风格获取活动
  const getActivitiesByStyle = (style) => {
    const activitiesMap = {
      relaxed: [
        { name: '品茶', type: 'leisure', cost: 50 },
        { name: '公园漫步', type: 'leisure', cost: 0 },
        { name: '购物', type: 'shopping', cost: 200 }
      ],
      adventurous: [
        { name: '徒步', type: 'adventure', cost: 0 },
        { name: '攀岩', type: 'adventure', cost: 100 },
        { name: '漂流', type: 'adventure', cost: 150 }
      ],
      cultural: [
        { name: '博物馆参观', type: 'cultural', cost: 30 },
        { name: '古建筑游览', type: 'cultural', cost: 40 },
        { name: '文化体验', type: 'cultural', cost: 80 }
      ]
    }
    
    return activitiesMap[style] || activitiesMap.relaxed
  }

  // 为每天选择景点
  const selectAttractionsForDay = (attractions, day, totalDays) => {
    const perDayCount = Math.min(3, Math.ceil(attractions.length / totalDays))
    const startIndex = (day - 1) * perDayCount
    return attractions.slice(startIndex, startIndex + perDayCount)
  }

  // 生成每日活动安排
  const generateDayActivities = (attractions) => {
    const activities = []
    let currentTime = 9 // 从早上9点开始
    
    attractions.forEach((attraction, index) => {
      if (index > 0) {
        currentTime += parseInt(attraction.time) + 0.5 // 加上交通时间
      }
      
      const startHour = Math.floor(currentTime)
      const startMinute = (currentTime - startHour) * 60
      const endTime = currentTime + parseInt(attraction.time)
      const endHour = Math.floor(endTime)
      const endMinute = (endTime - endHour) * 60
      
      activities.push({
        time: `${startHour.toString().padStart(2, '0')}:${startMinute.toString().padStart(2, '0')}-${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`,
        name: attraction.name,
        location: attraction.location,
        cost: attraction.cost,
        type: 'attraction',
        transportation: transportation.value === 'public' ? '地铁/公交' : '自驾',
        notes: `建议游玩${attraction.time}`
      })
      
      currentTime = endTime + 1 // 加上休息时间
    })
    
    // 添加用餐安排
    activities.push({
      time: '12:00-13:30',
      name: '午餐',
      location: '当地餐厅',
      cost: 50,
      type: 'meal',
      transportation: '步行',
      notes: '品尝当地特色美食'
    })
    
    activities.push({
      time: '18:00-19:30',
      name: '晚餐',
      location: '推荐餐厅',
      cost: 80,
      type: 'meal',
      transportation: '步行',
      notes: '享受当地夜生活'
    })
    
    return activities
  }

  // 生成预算
  const generateBudget = () => {
    const baseBudget = budget.value
    const days = totalDays.value
    
    return {
      total: baseBudget,
      perPerson: baseBudget,
      transportation: Math.round(baseBudget * 0.3),
      localTransport: Math.round(baseBudget * 0.1),
      accommodation: Math.round(baseBudget * 0.25),
      food: Math.round(baseBudget * 0.25),
      tickets: Math.round(baseBudget * 0.1),
      shopping: Math.round(baseBudget * 0.05),
      others: Math.round(baseBudget * 0.05)
    }
  }

  // 重置数据
  const resetItinerary = () => {
    itinerary.value = null
    error.value = null
  }

  return {
    // 响应式数据
    destination,
    startDate,
    endDate,
    transportation,
    budget,
    travelStyle,
    itinerary,
    loading,
    error,
    
    // 计算属性
    totalDays,
    dateRange,
    
    // 方法
    generateItinerary,
    resetItinerary
  }
})
